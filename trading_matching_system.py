#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的交易撮合系统
支持上交所和深交所L2数据处理，实现集合竞价和连续竞价撮合
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
from collections import defaultdict, deque
from sortedcontainers import SortedDict
import logging
from typing import Dict, List, Tuple, Optional, Any
import json
import os

# 设置中文字体支持
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class OrderBook:
    """订单簿管理类"""
    
    def __init__(self):
        # 使用SortedDict实现高效的价格排序
        self.buy_orders = SortedDict()  # 买单：价格从高到低
        self.sell_orders = SortedDict()  # 卖单：价格从低到高
        self.order_sequence = 0  # 订单序号，用于时间优先
        
    def add_order(self, price: float, volume: int, side: str, order_id: str = None, timestamp: int = None) -> Dict:
        """添加订单"""
        self.order_sequence += 1
        order = {
            'order_id': order_id or f"order_{self.order_sequence}",
            'price': price,
            'volume': volume,
            'side': side,
            'sequence': self.order_sequence,
            'timestamp': timestamp or self.order_sequence,
            'original_volume': volume
        }
        
        if side == 'B':  # 买单
            if price not in self.buy_orders:
                self.buy_orders[price] = deque()
            self.buy_orders[price].append(order)
        else:  # 卖单
            if price not in self.sell_orders:
                self.sell_orders[price] = deque()
            self.sell_orders[price].append(order)
            
        return order
    
    def cancel_order(self, order_id: str, price: float, side: str) -> bool:
        """撤销订单"""
        orders_dict = self.buy_orders if side == 'B' else self.sell_orders
        
        if price in orders_dict:
            orders_queue = orders_dict[price]
            for i, order in enumerate(orders_queue):
                if order['order_id'] == order_id:
                    del orders_queue[i]
                    if not orders_queue:
                        del orders_dict[price]
                    return True
        return False
    
    def get_best_bid(self) -> Optional[float]:
        """获取最优买价"""
        return self.buy_orders.peekitem(-1)[0] if self.buy_orders else None
    
    def get_best_ask(self) -> Optional[float]:
        """获取最优卖价"""
        return self.sell_orders.peekitem(0)[0] if self.sell_orders else None
    
    def get_market_depth(self, levels: int = 10) -> Tuple[List, List, List, List]:
        """获取市场深度数据"""
        bid_prices, bid_volumes = [], []
        ask_prices, ask_volumes = [], []
        
        # 买单深度（价格从高到低）
        for i, (price, orders) in enumerate(reversed(self.buy_orders.items())):
            if i >= levels:
                break
            total_volume = sum(order['volume'] for order in orders)
            if total_volume > 0:
                bid_prices.append(price)
                bid_volumes.append(total_volume)
        
        # 卖单深度（价格从低到高）
        for i, (price, orders) in enumerate(self.sell_orders.items()):
            if i >= levels:
                break
            total_volume = sum(order['volume'] for order in orders)
            if total_volume > 0:
                ask_prices.append(price)
                ask_volumes.append(total_volume)
        
        # 补齐到指定层数
        while len(bid_prices) < levels:
            bid_prices.append(0)
            bid_volumes.append(0)
        while len(ask_prices) < levels:
            ask_prices.append(0)
            ask_volumes.append(0)
            
        return bid_prices, bid_volumes, ask_prices, ask_volumes


class MatchingEngine:
    """撮合引擎"""
    
    def __init__(self):
        self.order_book = OrderBook()
        self.trades = []
        self.tick_data = []
        self.current_price = 0.0
        self.total_volume = 0
        self.total_value = 0.0
        self.num_trades = 0
        
    def process_order(self, order_data: Dict, trading_phase: str) -> List[Dict]:
        """处理订单"""
        if trading_phase in ['C', 'C111']:  # 集合竞价阶段
            return self._process_call_auction_order(order_data)
        else:  # 连续竞价阶段
            return self._process_continuous_order(order_data)
    
    def _process_call_auction_order(self, order_data: Dict) -> List[Dict]:
        """处理集合竞价订单（只接受不撮合）"""
        if order_data.get('order_type') == 'A':  # 新增订单
            self.order_book.add_order(
                price=order_data['order_price'],
                volume=order_data['order_volume'],
                side=order_data['order_side'],
                order_id=str(order_data.get('order_index', '')),
                timestamp=order_data.get('biz_index', 0)
            )
        elif order_data.get('order_type') == 'D':  # 撤单
            self.order_book.cancel_order(
                order_id=str(order_data.get('order_origin_no', '')),
                price=order_data['order_price'],
                side=order_data['order_side']
            )
        return []  # 集合竞价阶段不产生成交
    
    def _process_continuous_order(self, order_data: Dict) -> List[Dict]:
        """处理连续竞价订单（实时撮合）"""
        trades = []
        
        if order_data.get('order_type') == 'A':  # 新增订单
            trades = self._match_order(order_data)
        elif order_data.get('order_type') == 'D':  # 撤单
            self.order_book.cancel_order(
                order_id=str(order_data.get('order_origin_no', '')),
                price=order_data['order_price'],
                side=order_data['order_side']
            )
        
        return trades
    
    def _match_order(self, order_data: Dict) -> List[Dict]:
        """撮合订单"""
        trades = []
        remaining_volume = order_data['order_volume']
        order_price = order_data['order_price']
        order_side = order_data['order_side']
        
        if order_side == 'B':  # 买单
            # 与卖单撮合
            while remaining_volume > 0 and self.order_book.sell_orders:
                best_ask = self.order_book.get_best_ask()
                if best_ask is None or order_price < best_ask:
                    break
                    
                # 执行撮合
                sell_orders = self.order_book.sell_orders[best_ask]
                while remaining_volume > 0 and sell_orders:
                    sell_order = sell_orders[0]
                    trade_volume = min(remaining_volume, sell_order['volume'])
                    trade_price = best_ask
                    
                    # 创建成交记录
                    trade = {
                        'price': trade_price,
                        'volume': trade_volume,
                        'buy_order_id': str(order_data.get('order_index', '')),
                        'sell_order_id': sell_order['order_id'],
                        'timestamp': order_data.get('biz_index', 0)
                    }
                    trades.append(trade)
                    
                    # 更新统计信息
                    self.current_price = trade_price
                    self.total_volume += trade_volume
                    self.total_value += trade_price * trade_volume
                    self.num_trades += 1
                    
                    # 更新订单
                    remaining_volume -= trade_volume
                    sell_order['volume'] -= trade_volume
                    
                    if sell_order['volume'] == 0:
                        sell_orders.popleft()
                
                if not sell_orders:
                    del self.order_book.sell_orders[best_ask]
        
        else:  # 卖单
            # 与买单撮合
            while remaining_volume > 0 and self.order_book.buy_orders:
                best_bid = self.order_book.get_best_bid()
                if best_bid is None or order_price > best_bid:
                    break
                    
                # 执行撮合
                buy_orders = self.order_book.buy_orders[best_bid]
                while remaining_volume > 0 and buy_orders:
                    buy_order = buy_orders[0]
                    trade_volume = min(remaining_volume, buy_order['volume'])
                    trade_price = best_bid
                    
                    # 创建成交记录
                    trade = {
                        'price': trade_price,
                        'volume': trade_volume,
                        'buy_order_id': buy_order['order_id'],
                        'sell_order_id': str(order_data.get('order_index', '')),
                        'timestamp': order_data.get('biz_index', 0)
                    }
                    trades.append(trade)
                    
                    # 更新统计信息
                    self.current_price = trade_price
                    self.total_volume += trade_volume
                    self.total_value += trade_price * trade_volume
                    self.num_trades += 1
                    
                    # 更新订单
                    remaining_volume -= trade_volume
                    buy_order['volume'] -= trade_volume
                    
                    if buy_order['volume'] == 0:
                        buy_orders.popleft()
                
                if not buy_orders:
                    del self.order_book.buy_orders[best_bid]
        
        # 如果还有剩余量，加入订单簿
        if remaining_volume > 0:
            self.order_book.add_order(
                price=order_price,
                volume=remaining_volume,
                side=order_side,
                order_id=str(order_data.get('order_index', '')),
                timestamp=order_data.get('biz_index', 0)
            )
        
        return trades

    def call_auction_match(self) -> List[Dict]:
        """集合竞价撮合"""
        trades = []

        # 计算集合竞价价格（最大成交量原则）
        auction_price = self._calculate_auction_price()
        if auction_price is None:
            return trades

        # 按集合竞价价格撮合
        buy_volume = 0
        sell_volume = 0

        # 统计可成交的买单量
        for price, orders in reversed(self.order_book.buy_orders.items()):
            if price >= auction_price:
                buy_volume += sum(order['volume'] for order in orders)

        # 统计可成交的卖单量
        for price, orders in self.order_book.sell_orders.items():
            if price <= auction_price:
                sell_volume += sum(order['volume'] for order in orders)

        # 实际成交量
        match_volume = min(buy_volume, sell_volume)
        if match_volume == 0:
            return trades

        # 执行撮合
        remaining_match_volume = match_volume

        # 处理买单
        buy_orders_to_remove = []
        for price in reversed(list(self.order_book.buy_orders.keys())):
            if price < auction_price or remaining_match_volume == 0:
                break

            orders = self.order_book.buy_orders[price]
            orders_to_remove = []

            for i, order in enumerate(orders):
                if remaining_match_volume == 0:
                    break

                trade_volume = min(order['volume'], remaining_match_volume)

                # 创建成交记录
                trade = {
                    'price': auction_price,
                    'volume': trade_volume,
                    'buy_order_id': order['order_id'],
                    'sell_order_id': 'AUCTION',
                    'timestamp': order['timestamp']
                }
                trades.append(trade)

                remaining_match_volume -= trade_volume
                order['volume'] -= trade_volume

                if order['volume'] == 0:
                    orders_to_remove.append(i)

            # 移除已完全成交的订单
            for i in reversed(orders_to_remove):
                orders.pop(i)

            if not orders:
                buy_orders_to_remove.append(price)

        # 移除空的价格层
        for price in buy_orders_to_remove:
            del self.order_book.buy_orders[price]

        # 处理卖单
        remaining_match_volume = match_volume
        sell_orders_to_remove = []

        for price in list(self.order_book.sell_orders.keys()):
            if price > auction_price or remaining_match_volume == 0:
                break

            orders = self.order_book.sell_orders[price]
            orders_to_remove = []

            for i, order in enumerate(orders):
                if remaining_match_volume == 0:
                    break

                trade_volume = min(order['volume'], remaining_match_volume)
                remaining_match_volume -= trade_volume
                order['volume'] -= trade_volume

                if order['volume'] == 0:
                    orders_to_remove.append(i)

            # 移除已完全成交的订单
            for i in reversed(orders_to_remove):
                orders.pop(i)

            if not orders:
                sell_orders_to_remove.append(price)

        # 移除空的价格层
        for price in sell_orders_to_remove:
            del self.order_book.sell_orders[price]

        # 更新统计信息
        if trades:
            self.current_price = auction_price
            self.total_volume += match_volume
            self.total_value += auction_price * match_volume
            self.num_trades += len(trades)

        return trades

    def _calculate_auction_price(self) -> Optional[float]:
        """计算集合竞价价格（最大成交量原则）"""
        if not self.order_book.buy_orders or not self.order_book.sell_orders:
            return None

        # 获取所有可能的价格点
        all_prices = set()
        all_prices.update(self.order_book.buy_orders.keys())
        all_prices.update(self.order_book.sell_orders.keys())
        all_prices = sorted(all_prices)

        max_volume = 0
        best_price = None

        for price in all_prices:
            # 计算在此价格下的可成交量
            buy_volume = 0
            sell_volume = 0

            # 统计愿意以此价格或更高价格买入的订单
            for buy_price, orders in self.order_book.buy_orders.items():
                if buy_price >= price:
                    buy_volume += sum(order['volume'] for order in orders)

            # 统计愿意以此价格或更低价格卖出的订单
            for sell_price, orders in self.order_book.sell_orders.items():
                if sell_price <= price:
                    sell_volume += sum(order['volume'] for order in orders)

            match_volume = min(buy_volume, sell_volume)

            if match_volume > max_volume:
                max_volume = match_volume
                best_price = price

        return best_price

    def generate_tick(self, timestamp: str, trading_phase: str) -> Dict:
        """生成tick数据"""
        bid_prices, bid_volumes, ask_prices, ask_volumes = self.order_book.get_market_depth(10)

        # 计算加权平均价格和总量
        total_bid_qty = sum(bid_volumes)
        total_offer_qty = sum(ask_volumes)

        weighted_avg_bid_price = 0.0
        if total_bid_qty > 0:
            weighted_avg_bid_price = sum(p * v for p, v in zip(bid_prices, bid_volumes) if p > 0) / total_bid_qty

        weighted_avg_offer_price = 0.0
        if total_offer_qty > 0:
            weighted_avg_offer_price = sum(p * v for p, v in zip(ask_prices, ask_volumes) if p > 0) / total_offer_qty

        tick = {
            'timestamp': timestamp,
            'trading_phase': trading_phase,
            'bid_prices': bid_prices,
            'bid_volumes': bid_volumes,
            'offer_prices': ask_prices,
            'offer_volumes': ask_volumes,
            'num_trades': self.num_trades,
            'total_volume_trade': self.total_volume,
            'total_value_trade': self.total_value,
            'total_bid_qty': total_bid_qty,
            'weighted_avg_bid_price': weighted_avg_bid_price,
            'total_offer_qty': total_offer_qty,
            'weighted_avg_offer_price': weighted_avg_offer_price,
            'last_price': self.current_price
        }

        return tick


class TradingTimeManager:
    """交易时段管理器"""

    @staticmethod
    def get_trading_phase(time_str: str, market: str = 'shl2') -> str:
        """根据时间和市场确定交易阶段"""
        # 将时间字符串转换为时间对象
        if len(time_str) == 8:  # HHMMSSSS格式
            hour = int(time_str[:2])
            minute = int(time_str[2:4])
            second = int(time_str[4:6])
        else:
            return 'UNKNOWN'

        time_obj = time(hour, minute, second)

        # 集合竞价时段
        if time(9, 15) <= time_obj <= time(9, 25):
            return 'C'  # 开盘集合竞价
        elif market == 'szl2' and time(14, 57) <= time_obj <= time(15, 0):
            return 'C'  # 深交所收盘集合竞价
        # 连续竞价时段
        elif time(9, 30) <= time_obj <= time(11, 30):
            return 'T'  # 上午连续竞价
        elif time(13, 0) <= time_obj <= time(14, 57):
            return 'T'  # 下午连续竞价
        elif market == 'shl2' and time(14, 57) <= time_obj <= time(15, 0):
            # 上交所需要根据order数据中的eq_trading_phase_code判断
            return 'T'  # 默认连续竞价，具体需要根据数据判断
        else:
            return 'S'  # 停牌或其他状态


class DataProcessor:
    """数据处理器基类"""

    def __init__(self, security_id: str, market: str):
        self.security_id = security_id
        self.market = market
        self.matching_engine = MatchingEngine()
        self.time_manager = TradingTimeManager()
        self.processed_events = []

    def process_data_files(self, tick_file: str, trade_file: str = None, order_file: str = None) -> Dict:
        """处理数据文件"""
        raise NotImplementedError

    def save_results(self, output_dir: str = 'output'):
        """保存结果"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 保存生成的tick数据
        if self.matching_engine.tick_data:
            tick_df = pd.DataFrame(self.matching_engine.tick_data)
            tick_file = f"{output_dir}/generated_tick_{self.security_id}_{self.market}.csv"
            tick_df.to_csv(tick_file, index=False, encoding='utf-8-sig')
            logger.info(f"保存生成的tick数据到: {tick_file}")

        # 保存生成的trade数据
        if self.matching_engine.trades:
            trade_df = pd.DataFrame(self.matching_engine.trades)
            trade_file = f"{output_dir}/generated_trade_{self.security_id}_{self.market}.csv"
            trade_df.to_csv(trade_file, index=False, encoding='utf-8-sig')
            logger.info(f"保存生成的trade数据到: {trade_file}")


class SHExchangeProcessor(DataProcessor):
    """上交所数据处理器"""

    def __init__(self, security_id: str):
        super().__init__(security_id, 'shl2')

    def process_data_files(self, tick_file: str, trade_file: str, order_file: str) -> Dict:
        """处理上交所数据文件"""
        logger.info(f"开始处理上交所数据: {self.security_id}")

        # 读取数据文件
        logger.info("读取官方tick数据...")
        official_tick_df = pd.read_csv(tick_file)

        logger.info("读取trade数据...")
        trade_df = pd.read_csv(trade_file) if trade_file else pd.DataFrame()

        logger.info("读取order数据...")
        order_df = pd.read_csv(order_file) if order_file else pd.DataFrame()

        logger.info(f"数据统计: tick={len(official_tick_df)}, trade={len(trade_df)}, order={len(order_df)}")

        # 合并order和trade数据，按biz_index排序
        all_events = []

        # 添加order事件（只处理有效的biz_index）
        logger.info("处理order事件...")
        valid_orders = order_df[pd.notna(order_df.get('biz_index', pd.Series()))]
        for idx, (_, row) in enumerate(valid_orders.iterrows()):
            if idx % 10000 == 0:
                logger.info(f"处理order进度: {idx}/{len(valid_orders)}")

            event = {
                'type': 'order',
                'biz_index': row['biz_index'],
                'timestamp': row['time'],
                'data': row.to_dict()
            }
            all_events.append(event)

        # 添加trade事件
        logger.info("处理trade事件...")
        valid_trades = trade_df[pd.notna(trade_df.get('biz_index', pd.Series()))]
        for idx, (_, row) in enumerate(valid_trades.iterrows()):
            if idx % 10000 == 0:
                logger.info(f"处理trade进度: {idx}/{len(valid_trades)}")

            event = {
                'type': 'trade',
                'biz_index': row['biz_index'],
                'timestamp': row['time'],
                'data': row.to_dict()
            }
            all_events.append(event)

        # 按biz_index排序
        logger.info("排序事件...")
        all_events.sort(key=lambda x: x['biz_index'])

        # 逐个处理事件
        logger.info(f"开始处理 {len(all_events)} 个事件...")
        for idx, event in enumerate(all_events):
            if idx % 10000 == 0:
                logger.info(f"处理事件进度: {idx}/{len(all_events)}")
            self._process_event(event)

        # 生成最终的tick数据
        logger.info("生成最终tick数据...")
        self._generate_final_ticks(official_tick_df)

        result = {
            'generated_ticks': len(self.matching_engine.tick_data),
            'generated_trades': len(self.matching_engine.trades),
            'official_ticks': len(official_tick_df)
        }

        logger.info(f"处理完成: {result}")
        return result

    def _process_event(self, event: Dict):
        """处理单个事件"""
        if event['type'] == 'order':
            self._process_order_event(event)
        elif event['type'] == 'trade':
            self._process_trade_event(event)

    def _process_order_event(self, event: Dict):
        """处理order事件"""
        data = event['data']

        # 确定交易阶段
        trading_phase = data.get('eq_trading_phase_code', 'T')
        if not trading_phase:
            trading_phase = self.time_manager.get_trading_phase(str(data['time']), self.market)

        # 处理订单
        if pd.notna(data.get('order_price')) and pd.notna(data.get('order_volume')):
            order_data = {
                'order_type': data.get('order_type'),
                'order_price': float(data['order_price']),
                'order_volume': int(data['order_volume']),
                'order_side': data.get('order_side'),
                'order_index': data.get('order_index'),
                'order_origin_no': data.get('order_origin_no'),
                'biz_index': data.get('biz_index')
            }

            trades = self.matching_engine.process_order(order_data, trading_phase)

            # 如果是集合竞价结束，执行集合竞价撮合
            if trading_phase == 'C' and self._is_auction_end_time(str(data['time'])):
                auction_trades = self.matching_engine.call_auction_match()
                trades.extend(auction_trades)

            # 生成tick数据
            if trades or self._should_generate_tick(data):
                tick = self.matching_engine.generate_tick(str(data['time']), trading_phase)
                self.matching_engine.tick_data.append(tick)

    def _process_trade_event(self, event: Dict):
        """处理trade事件（上交所的trade数据是已经成交的）"""
        data = event['data']

        # 更新统计信息
        if pd.notna(data.get('trade_volume')) and pd.notna(data.get('trade_price')):
            volume = float(data['trade_volume'])
            price = float(data['trade_price'])

            self.matching_engine.current_price = price
            self.matching_engine.total_volume += volume
            self.matching_engine.total_value += price * volume
            self.matching_engine.num_trades += 1

            # 记录trade
            trade = {
                'price': price,
                'volume': volume,
                'buy_order_id': str(data.get('trade_buy_no', '')),
                'sell_order_id': str(data.get('trade_sell_no', '')),
                'timestamp': data.get('biz_index', 0),
                'trade_bs_flag': data.get('trade_bs_flag')
            }
            self.matching_engine.trades.append(trade)

    def _is_auction_end_time(self, time_str: str) -> bool:
        """判断是否为集合竞价结束时间"""
        if len(time_str) >= 6:
            hour = int(time_str[:2])
            minute = int(time_str[2:4])
            return (hour == 9 and minute == 25) or (hour == 15 and minute == 0)
        return False

    def _should_generate_tick(self, data: Dict) -> bool:
        """判断是否应该生成tick数据"""
        # 可以根据具体需求调整生成tick的条件
        return True

    def _generate_final_ticks(self, official_tick_df: pd.DataFrame):
        """生成最终的tick数据，与官方格式对齐"""
        # 这里可以根据官方tick数据的时间戳，补充缺失的tick
        pass


class SZExchangeProcessor(DataProcessor):
    """深交所数据处理器"""

    def __init__(self, security_id: str):
        super().__init__(security_id, 'szl2')

    def process_data_files(self, tick_file: str, trade_file: Optional[str] = None, order_file: Optional[str] = None) -> Dict:
        """处理深交所数据文件"""
        logger.info(f"开始处理深交所数据: {self.security_id}")

        # 读取数据文件
        official_tick_df = pd.read_csv(tick_file)
        order_df = pd.read_csv(order_file) if order_file else pd.DataFrame()

        # 深交所主要从order数据生成trade和tick
        if not order_df.empty:
            # 按order_index排序处理order数据
            order_df = order_df.sort_values('order_index')

            for _, row in order_df.iterrows():
                self._process_sz_order(row.to_dict())

        return {
            'generated_ticks': len(self.matching_engine.tick_data),
            'generated_trades': len(self.matching_engine.trades),
            'official_ticks': len(official_tick_df)
        }

    def _process_sz_order(self, order_data: Dict):
        """处理深交所订单"""
        # 确定交易阶段
        trading_phase = self.time_manager.get_trading_phase(str(order_data['time']), self.market)

        # 深交所order_type: 1=撤单, 2=下单
        if order_data.get('order_type') == 2:  # 下单
            if order_data.get('order_price', 0) > 0:  # 正常下单
                processed_order = {
                    'order_type': 'A',
                    'order_price': float(order_data['order_price']),
                    'order_volume': int(order_data['order_volume']),
                    'order_side': order_data.get('order_side'),
                    'order_index': order_data.get('order_index'),
                    'biz_index': order_data.get('order_index')  # 深交所使用order_index作为序号
                }
            else:  # 撤单（价格为0）
                processed_order = {
                    'order_type': 'D',
                    'order_price': 0,
                    'order_volume': int(order_data['order_volume']),
                    'order_side': order_data.get('order_side'),
                    'order_index': order_data.get('order_index'),
                    'order_origin_no': order_data.get('order_index'),
                    'biz_index': order_data.get('order_index')
                }
        elif order_data.get('order_type') == 1:  # 撤单
            processed_order = {
                'order_type': 'D',
                'order_price': float(order_data.get('order_price', 0)),
                'order_volume': int(order_data['order_volume']),
                'order_side': order_data.get('order_side'),
                'order_index': order_data.get('order_index'),
                'order_origin_no': order_data.get('order_index'),
                'biz_index': order_data.get('order_index')
            }
        else:
            return

        # 处理订单
        trades = self.matching_engine.process_order(processed_order, trading_phase)

        # 如果是集合竞价结束，执行集合竞价撮合
        if trading_phase == 'C' and self._is_auction_end_time(str(order_data['time'])):
            auction_trades = self.matching_engine.call_auction_match()
            trades.extend(auction_trades)

        # 生成tick数据
        if trades or self._should_generate_tick(order_data):
            tick = self.matching_engine.generate_tick(str(order_data['time']), trading_phase)
            self.matching_engine.tick_data.append(tick)

    def _is_auction_end_time(self, time_str: str) -> bool:
        """判断是否为集合竞价结束时间"""
        if len(time_str) >= 6:
            hour = int(time_str[:2])
            minute = int(time_str[2:4])
            return (hour == 9 and minute == 25) or (hour == 15 and minute == 0)
        return False

    def _should_generate_tick(self, data: Dict) -> bool:
        """判断是否应该生成tick数据"""
        return True


class TradingMatchingSystem:
    """完整的交易撮合系统"""

    def __init__(self):
        self.processors = {}
        self.results = {}

    def process_security(self, security_id: str, market: str, data_files: Dict) -> Dict:
        """处理单个证券的数据"""
        logger.info(f"开始处理证券: {security_id} ({market})")

        # 创建对应的处理器
        if market == 'shl2':
            processor = SHExchangeProcessor(security_id)
        elif market == 'szl2':
            processor = SZExchangeProcessor(security_id)
        else:
            raise ValueError(f"不支持的市场: {market}")

        # 处理数据
        result = processor.process_data_files(
            tick_file=data_files.get('tick'),
            trade_file=data_files.get('trade'),
            order_file=data_files.get('order')
        )

        # 保存结果
        processor.save_results()

        # 存储处理器和结果
        self.processors[f"{security_id}_{market}"] = processor
        self.results[f"{security_id}_{market}"] = result

        return result

    def validate_results(self, security_id: str, market: str, official_tick_file: str) -> Dict:
        """验证生成的数据与官方数据的一致性"""
        key = f"{security_id}_{market}"
        if key not in self.processors:
            raise ValueError(f"未找到证券 {security_id} 的处理结果")

        processor = self.processors[key]
        official_df = pd.read_csv(official_tick_file)

        # 比较生成的tick数据与官方数据
        validation_result = self._compare_tick_data(
            processor.matching_engine.tick_data,
            official_df
        )

        return validation_result

    def _compare_tick_data(self, generated_ticks: List[Dict], official_df: pd.DataFrame) -> Dict:
        """比较生成的tick数据与官方数据"""
        comparison_result = {
            'total_generated': len(generated_ticks),
            'total_official': len(official_df),
            'matches': 0,
            'mismatches': 0,
            'missing_in_generated': 0,
            'extra_in_generated': 0,
            'sample_mismatches': [],
            'field_comparisons': {}
        }

        # 创建生成数据的时间戳索引
        generated_by_time = {str(tick['timestamp']): tick for tick in generated_ticks}

        # 比较每个官方tick数据点
        for idx, official_row in official_df.iterrows():
            official_time = str(official_row['time'])

            if official_time in generated_by_time:
                generated_tick = generated_by_time[official_time]

                # 比较关键字段
                field_match = self._compare_tick_fields(official_row, generated_tick)

                if field_match['is_match']:
                    comparison_result['matches'] += 1
                else:
                    comparison_result['mismatches'] += 1
                    if len(comparison_result['sample_mismatches']) < 5:
                        comparison_result['sample_mismatches'].append({
                            'timestamp': official_time,
                            'differences': field_match['differences']
                        })

                # 累计字段比较统计
                for field, stats in field_match['field_stats'].items():
                    if field not in comparison_result['field_comparisons']:
                        comparison_result['field_comparisons'][field] = {'matches': 0, 'total': 0}
                    comparison_result['field_comparisons'][field]['matches'] += stats['match']
                    comparison_result['field_comparisons'][field]['total'] += 1
            else:
                comparison_result['missing_in_generated'] += 1

        # 计算额外生成的数据
        official_times = set(str(row['time']) for _, row in official_df.iterrows())
        generated_times = set(generated_by_time.keys())
        comparison_result['extra_in_generated'] = len(generated_times - official_times)

        # 计算字段匹配率
        for field, stats in comparison_result['field_comparisons'].items():
            stats['match_rate'] = stats['matches'] / stats['total'] if stats['total'] > 0 else 0

        logger.info(f"数据比较结果: {comparison_result}")
        return comparison_result

    def _compare_tick_fields(self, official_row: pd.Series, generated_tick: Dict) -> Dict:
        """比较单个tick的字段"""
        result = {
            'is_match': True,
            'differences': [],
            'field_stats': {}
        }

        # 比较关键数值字段
        key_fields = [
            'num_trades', 'total_volume_trade', 'total_value_trade',
            'total_bid_qty', 'total_offer_qty'
        ]

        for field in key_fields:
            official_val = official_row.get(field, 0)
            generated_val = generated_tick.get(field, 0)

            # 处理NaN值
            if pd.isna(official_val):
                official_val = 0
            if pd.isna(generated_val):
                generated_val = 0

            is_match = abs(float(official_val) - float(generated_val)) < 0.01
            result['field_stats'][field] = {'match': 1 if is_match else 0}

            if not is_match:
                result['is_match'] = False
                result['differences'].append({
                    'field': field,
                    'official': official_val,
                    'generated': generated_val
                })

        return result

    def generate_report(self, output_file: str = 'matching_report.json'):
        """生成处理报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'processed_securities': list(self.results.keys()),
            'results': self.results,
            'summary': {
                'total_securities': len(self.results),
                'total_generated_ticks': sum(r.get('generated_ticks', 0) for r in self.results.values()),
                'total_generated_trades': sum(r.get('generated_trades', 0) for r in self.results.values())
            }
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"处理报告已保存到: {output_file}")
        return report


def main():
    """主函数"""
    # 创建交易撮合系统
    system = TradingMatchingSystem()

    # 定义数据文件
    data_configs = [
        {
            'security_id': '518880',
            'market': 'shl2',
            'files': {
                'tick': 'data/hq-shl2-518880-1-20250807165431662.csv',
                'trade': 'data/hq-shl2-518880-3-20250807134544227.csv',
                'order': 'data/hq-shl2-518880-4-20250807133643043.csv'
            }
        },
        {
            'security_id': '161116',
            'market': 'szl2',
            'files': {
                'tick': 'data/hq-szl2-161116-1-20250807165417054.csv',
                'trade': 'data/hq-szl2-161116-3-20250807134652012.csv',
                'order': 'data/hq-szl2-161116-4-20250807134642187.csv'
            }
        }
    ]

    # 处理每个证券
    for config in data_configs:
        try:
            result = system.process_security(
                security_id=config['security_id'],
                market=config['market'],
                data_files=config['files']
            )
            logger.info(f"处理完成: {config['security_id']} - {result}")

            # 验证结果
            validation = system.validate_results(
                security_id=config['security_id'],
                market=config['market'],
                official_tick_file=config['files']['tick']
            )
            logger.info(f"验证结果: {validation}")

        except Exception as e:
            logger.error(f"处理证券 {config['security_id']} 时出错: {e}")
            continue

    # 生成报告
    report = system.generate_report()
    print("\n=== 处理完成 ===")
    print(f"处理了 {report['summary']['total_securities']} 个证券")
    print(f"生成了 {report['summary']['total_generated_ticks']} 条tick数据")
    print(f"生成了 {report['summary']['total_generated_trades']} 条trade数据")


if __name__ == "__main__":
    main()
