#!/usr/bin/env python3
"""专门调试集合竞价处理逻辑"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_parser import SZExchangeParser
from matching_engine import MatchingEngine
import pandas as pd
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CallAuctionDebugger:
    """集合竞价调试器"""
    
    def __init__(self):
        self.matching_engine = MatchingEngine()
        
    def debug_call_auction_processing(self, security_id='161116'):
        """调试集合竞价处理"""
        print(f"=== 集合竞价处理调试 ({security_id}) ===")
        
        # 解析数据
        parser = SZExchangeParser(security_id)
        order_file = f'data/hq-szl2-{security_id}-4-20250807134642187.csv'
        tick_file = f'data/hq-szl2-{security_id}-1-20250807165417054.csv'
        
        orders, _ = parser.parse_data_files(tick_file, None, order_file)
        
        print(f"总订单数: {len(orders)}")
        
        # 分析订单时间分布
        self.analyze_order_time_distribution(orders)
        
        # 专门处理集合竞价订单
        self.process_call_auction_orders(orders)
        
        # 验证集合竞价撮合逻辑
        self.test_call_auction_matching(orders)
    
    def analyze_order_time_distribution(self, orders):
        """分析订单时间分布"""
        print(f"\n=== 订单时间分布分析 ===")
        
        from data_parser import TimeParser
        
        # 按交易阶段分类
        phase_stats = {}
        time_ranges = {
            'pre_market': (0, 91500000),           # 开盘前
            'call_auction': (91500000, 92500000),  # 集合竞价
            'continuous': (92500000, 113000000),   # 连续竞价上午
            'lunch_break': (113000000, 130000000), # 午休
            'afternoon': (130000000, 150000000),   # 下午连续竞价
            'after_market': (150000000, 999999999) # 收盘后
        }
        
        for order in orders[:500]:  # 分析前500个订单
            phase = TimeParser.get_trading_phase(str(order.time), 'szl2')
            if phase not in phase_stats:
                phase_stats[phase] = []
            phase_stats[phase].append(order)
        
        print(f"交易阶段统计:")
        for phase, orders_in_phase in phase_stats.items():
            print(f"  {phase}: {len(orders_in_phase)} 个订单")
            if orders_in_phase:
                times = [o.time for o in orders_in_phase]
                print(f"    时间范围: {min(times)} - {max(times)}")
        
        # 检查集合竞价时间段的订单
        call_auction_orders = [o for o in orders if 91500000 <= o.time <= 92500000]
        print(f"\n集合竞价时间段订单详情:")
        print(f"  总数: {len(call_auction_orders)}")
        
        if call_auction_orders:
            print(f"  前10个集合竞价订单:")
            for i, order in enumerate(call_auction_orders[:10]):
                phase = TimeParser.get_trading_phase(str(order.time), 'szl2')
                print(f"    {i+1}: {order.date}_{order.time} ({phase}) {order.order_type} {order.side} {order.volume}@{order.price}")
    
    def process_call_auction_orders(self, orders):
        """处理集合竞价订单"""
        print(f"\n=== 集合竞价订单处理 ===")
        
        from data_parser import TimeParser
        
        # 找到所有集合竞价订单
        call_auction_orders = []
        for order in orders:
            phase = TimeParser.get_trading_phase(str(order.time), 'szl2')
            if phase == 'C':
                call_auction_orders.append(order)
        
        print(f"集合竞价订单总数: {len(call_auction_orders)}")
        
        if not call_auction_orders:
            print("❌ 没有找到集合竞价订单！")
            return
        
        # 按时间排序集合竞价订单
        call_auction_orders.sort(key=lambda x: x.time)
        
        print(f"集合竞价订单时间范围: {call_auction_orders[0].time} - {call_auction_orders[-1].time}")
        
        # 逐个处理集合竞价订单
        print(f"\n处理集合竞价订单:")
        for i, order in enumerate(call_auction_orders):
            before_state = self.get_order_book_summary()
            
            # 处理订单
            generated_trades = self.matching_engine.process_order(order, 'C')
            
            after_state = self.get_order_book_summary()
            
            print(f"  {i+1}: {order.time} {order.order_type} {order.side} {order.volume}@{order.price}")
            print(f"      订单簿: {before_state} -> {after_state}")
            print(f"      生成成交: {len(generated_trades)} 笔")
            
            if i >= 20:  # 只显示前20个
                print(f"  ... (还有 {len(call_auction_orders) - 20} 个订单)")
                break
        
        # 检查集合竞价结束时的订单簿状态
        final_state = self.get_order_book_summary()
        print(f"\n集合竞价结束时订单簿状态: {final_state}")
        
        # 执行集合竞价撮合
        print(f"\n执行集合竞价撮合...")
        auction_trades = self.matching_engine.call_auction_match()
        print(f"集合竞价撮合生成成交: {len(auction_trades)} 笔")
        
        if auction_trades:
            print(f"集合竞价成交详情:")
            for i, trade in enumerate(auction_trades[:5]):
                print(f"  {i+1}: {trade.price}@{trade.volume}")
        
        # 检查撮合后的订单簿状态
        after_auction_state = self.get_order_book_summary()
        print(f"集合竞价撮合后订单簿状态: {after_auction_state}")
    
    def test_call_auction_matching(self, orders=None):
        """测试集合竞价撮合逻辑"""
        print(f"\n=== 集合竞价撮合逻辑测试 ===")

        # 检查实际订单的日期字段
        if orders:
            print(f"\n检查实际订单的日期字段:")
            for i, order in enumerate(orders[:10]):
                print(f"  订单 {i+1}: date='{order.date}' (type: {type(order.date)})")

        # 检查日期比较逻辑
        print(f"\n日期比较测试:")
        test_dates = ['20250805', '20250806', 20250805, 20250806]
        for i, date in enumerate(test_dates):
            print(f"  {date} (type: {type(date)}) == '20250805': {date == '20250805'}")
            print(f"  {date} (type: {type(date)}) == 20250806: {date == '20250806'}")
            print(f"  str({date}) == '20250805': {str(date) == '20250805'}")

        # 重置订单簿
        self.matching_engine = MatchingEngine()

        # 手动添加一些测试订单（使用相同的日期）
        from data_parser import StandardizedOrder

        # 正确的参数顺序：order_id, time, price, volume, side, order_type, origin_no, date
        test_orders = [
            StandardizedOrder('1', 91500000, 131.0, 1000, 'B', 'A', '1', '20250805'),
            StandardizedOrder('2', 91500100, 131.1, 2000, 'B', 'A', '2', '20250805'),
            StandardizedOrder('3', 91500200, 131.2, 1500, 'S', 'A', '3', '20250805'),
            StandardizedOrder('4', 91500300, 131.1, 800, 'S', 'A', '4', '20250805'),
            StandardizedOrder('5', 91500400, 131.0, 500, 'B', 'A', '5', '20250805'),
        ]
        
        print(f"添加测试订单:")
        for order in test_orders:
            print(f"  {order.order_type} {order.side} {order.volume}@{order.price}")
            self.matching_engine.process_order(order, 'C')
        
        state_before = self.get_order_book_summary()
        print(f"\n集合竞价前订单簿: {state_before}")
        
        # 执行集合竞价
        auction_trades = self.matching_engine.call_auction_match()
        
        state_after = self.get_order_book_summary()
        print(f"集合竞价后订单簿: {state_after}")
        print(f"生成成交: {len(auction_trades)} 笔")
        
        if auction_trades:
            for i, trade in enumerate(auction_trades):
                print(f"  成交 {i+1}: {trade.price}@{trade.volume}")
    
    def get_order_book_summary(self):
        """获取订单簿摘要"""
        buy_levels = len(self.matching_engine.order_book.buy_orders)
        sell_levels = len(self.matching_engine.order_book.sell_orders)
        
        total_buy_qty = sum(
            sum(order['volume'] for order in orders)
            for orders in self.matching_engine.order_book.buy_orders.values()
        )
        
        total_sell_qty = sum(
            sum(order['volume'] for order in orders)
            for orders in self.matching_engine.order_book.sell_orders.values()
        )
        
        return {
            'buy_levels': buy_levels,
            'sell_levels': sell_levels,
            'total_buy_qty': total_buy_qty,
            'total_sell_qty': total_sell_qty
        }

def main():
    debugger = CallAuctionDebugger()
    debugger.debug_call_auction_processing('161116')

if __name__ == "__main__":
    main()
