#!/usr/bin/env python3
"""深度调试订单簿构建逻辑"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_parser import SZExchangeParser
from matching_engine import MatchingEngine
from snapshot_generator import SnapshotGenerator, SnapshotConfig
import pandas as pd
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OrderBookDebugger:
    """订单簿调试器"""
    
    def __init__(self):
        self.matching_engine = MatchingEngine()
        self.snapshot_generator = None
        self.debug_log = []
        
    def debug_order_processing(self, security_id='161116', max_orders=100):
        """调试订单处理流程"""
        print(f"=== 深度调试订单簿构建逻辑 ({security_id}) ===")
        
        # 解析数据
        parser = SZExchangeParser(security_id)
        order_file = f'data/hq-szl2-{security_id}-4-20250807134642187.csv'
        tick_file = f'data/hq-szl2-{security_id}-1-20250807165417054.csv'
        
        orders, _ = parser.parse_data_files(tick_file, None, order_file)
        
        print(f"总订单数: {len(orders)}")
        print(f"调试前{max_orders}个订单的处理过程")
        
        # 加载官方tick数据用于对比
        official_tick_df = pd.read_csv(tick_file)
        
        # 创建快照生成器
        snapshot_config = SnapshotConfig()
        snapshot_config.mode = 'event_driven'
        snapshot_config.levels = 10
        self.snapshot_generator = SnapshotGenerator(snapshot_config)
        
        # 逐个处理订单并记录详细信息
        for i, order in enumerate(orders[:max_orders]):
            if i % 10 == 0:
                print(f"\n--- 处理第 {i+1} 个订单 ---")
            
            self.debug_single_order(order, i+1)
            
            # 在关键时间点生成快照进行对比
            if self.is_key_timestamp(order.time):
                self.compare_with_official_data(order, official_tick_df)
        
        # 分析调试结果
        self.analyze_debug_results()
    
    def debug_single_order(self, order, order_index):
        """调试单个订单处理"""
        from data_parser import TimeParser
        
        # 记录订单信息
        debug_info = {
            'order_index': order_index,
            'order_id': order.order_id,
            'time': order.time,
            'date': order.date,
            'price': order.price,
            'volume': order.volume,
            'side': order.side,
            'order_type': order.order_type,
            'trading_phase': TimeParser.get_trading_phase(str(order.time), 'szl2')
        }
        
        # 记录处理前的订单簿状态
        before_state = self.get_order_book_summary()
        debug_info['before_state'] = before_state
        
        # 处理订单
        trading_phase = debug_info['trading_phase']
        generated_trades = self.matching_engine.process_order(order, trading_phase)
        
        # 记录处理后的订单簿状态
        after_state = self.get_order_book_summary()
        debug_info['after_state'] = after_state
        debug_info['trades_generated'] = len(generated_trades)
        
        # 记录状态变化
        debug_info['state_changed'] = before_state != after_state
        
        self.debug_log.append(debug_info)
        
        # 打印关键信息
        if debug_info['state_changed'] or generated_trades:
            print(f"  订单 {order_index}: {order.order_type} {order.side} {order.volume}@{order.price}")
            print(f"    时间: {order.date}_{order.time} ({trading_phase})")
            print(f"    订单簿变化: {before_state} -> {after_state}")
            if generated_trades:
                print(f"    生成成交: {len(generated_trades)} 笔")
    
    def get_order_book_summary(self):
        """获取订单簿摘要"""
        buy_levels = len(self.matching_engine.order_book.buy_orders)
        sell_levels = len(self.matching_engine.order_book.sell_orders)
        
        total_buy_qty = sum(
            sum(order['volume'] for order in orders)
            for orders in self.matching_engine.order_book.buy_orders.values()
        )
        
        total_sell_qty = sum(
            sum(order['volume'] for order in orders)
            for orders in self.matching_engine.order_book.sell_orders.values()
        )
        
        return {
            'buy_levels': buy_levels,
            'sell_levels': sell_levels,
            'total_buy_qty': total_buy_qty,
            'total_sell_qty': total_sell_qty
        }
    
    def is_key_timestamp(self, timestamp):
        """判断是否为关键时间戳"""
        key_times = [
            84418000,   # 8:44:18 - 开盘前
            91500000,   # 9:15:00 - 集合竞价开始
            92500000,   # 9:25:00 - 集合竞价结束
            93000000,   # 9:30:00 - 连续竞价开始
            113000000,  # 11:30:00 - 上午收盘
            130000000,  # 13:00:00 - 下午开盘
        ]
        return timestamp in key_times
    
    def compare_with_official_data(self, order, official_tick_df):
        """与官方数据对比"""
        # 查找对应的官方tick数据
        matching_ticks = official_tick_df[
            (official_tick_df['date'] == int(order.date)) &
            (official_tick_df['time'] == order.time)
        ]
        
        if len(matching_ticks) > 0:
            official_tick = matching_ticks.iloc[0]
            
            # 生成当前快照
            trading_phase = self.debug_log[-1]['trading_phase']
            current_snapshot = self.snapshot_generator.generate_snapshot(
                str(order.time), trading_phase, self.matching_engine
            )
            
            print(f"\n=== 关键时间点对比: {order.date}_{order.time} ===")
            print(f"官方数据:")
            print(f"  total_bid_qty: {official_tick.get('total_bid_qty', 0)}")
            print(f"  total_offer_qty: {official_tick.get('total_offer_qty', 0)}")
            print(f"  num_trades: {official_tick.get('num_trades', 0)}")
            
            print(f"生成数据:")
            print(f"  total_bid_qty: {current_snapshot.total_bid_qty}")
            print(f"  total_offer_qty: {current_snapshot.total_offer_qty}")
            print(f"  num_trades: {current_snapshot.num_trades}")
            
            # 计算差异
            bid_diff = abs(current_snapshot.total_bid_qty - float(official_tick.get('total_bid_qty', 0)))
            offer_diff = abs(current_snapshot.total_offer_qty - float(official_tick.get('total_offer_qty', 0)))
            
            print(f"差异:")
            print(f"  bid_qty差异: {bid_diff}")
            print(f"  offer_qty差异: {offer_diff}")
    
    def analyze_debug_results(self):
        """分析调试结果"""
        print(f"\n=== 调试结果分析 ===")
        
        total_orders = len(self.debug_log)
        state_changed_orders = sum(1 for log in self.debug_log if log['state_changed'])
        trade_generated_orders = sum(1 for log in self.debug_log if log['trades_generated'] > 0)
        
        print(f"总处理订单数: {total_orders}")
        print(f"引起订单簿变化的订单数: {state_changed_orders} ({state_changed_orders/total_orders*100:.1f}%)")
        print(f"生成成交的订单数: {trade_generated_orders} ({trade_generated_orders/total_orders*100:.1f}%)")
        
        # 按交易阶段分析
        phase_stats = {}
        for log in self.debug_log:
            phase = log['trading_phase']
            if phase not in phase_stats:
                phase_stats[phase] = {'count': 0, 'state_changed': 0, 'trades': 0}
            
            phase_stats[phase]['count'] += 1
            if log['state_changed']:
                phase_stats[phase]['state_changed'] += 1
            if log['trades_generated'] > 0:
                phase_stats[phase]['trades'] += 1
        
        print(f"\n按交易阶段统计:")
        for phase, stats in phase_stats.items():
            print(f"  {phase}: {stats['count']}订单, {stats['state_changed']}变化, {stats['trades']}成交")
        
        # 检查日期切换
        dates = set(log['date'] for log in self.debug_log)
        if len(dates) > 1:
            print(f"\n检测到跨日期数据: {sorted(dates)}")
            self.analyze_date_transition()
    
    def analyze_date_transition(self):
        """分析日期切换"""
        print(f"\n=== 日期切换分析 ===")
        
        # 找到日期切换点
        prev_date = None
        for i, log in enumerate(self.debug_log):
            if prev_date and log['date'] != prev_date:
                print(f"日期切换点: 订单 {i} 从 {prev_date} 切换到 {log['date']}")
                
                # 检查切换前后的订单簿状态
                if i > 0:
                    before_switch = self.debug_log[i-1]['after_state']
                    after_switch = log['before_state']
                    
                    print(f"  切换前订单簿: {before_switch}")
                    print(f"  切换后订单簿: {after_switch}")
                    
                    # 检查是否正确重置
                    if (after_switch['buy_levels'] > 0 or after_switch['sell_levels'] > 0 or
                        after_switch['total_buy_qty'] > 0 or after_switch['total_sell_qty'] > 0):
                        print(f"  ❌ 错误: 新交易日订单簿未正确重置!")
                    else:
                        print(f"  ✅ 正确: 新交易日订单簿已重置")
            
            prev_date = log['date']

def main():
    debugger = OrderBookDebugger()
    debugger.debug_order_processing('161116', max_orders=200)

if __name__ == "__main__":
    main()
