#!/usr/bin/env python3
"""测试修复后的撮合系统"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_matching_system import AdvancedMatchingSystem, ProcessingConfig
from snapshot_generator import SnapshotConfig
import pandas as pd
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_fixed_matching():
    """测试修复后的撮合系统"""
    print("=== 测试修复后的撮合系统 ===")
    
    # 配置
    config = ProcessingConfig()
    config.security_id = '161116'
    config.market = 'szl2'
    config.progress_report_interval = 5000
    
    # 数据文件
    tick_file = 'data/hq-szl2-161116-1-20250807165417054.csv'
    order_file = 'data/hq-szl2-161116-4-20250807134642187.csv'
    
    # 创建快照配置（只取前100个时间点进行快速测试）
    official_tick_df = pd.read_csv(tick_file)
    sample_ticks = official_tick_df.head(100)
    datetime_combinations = []
    for _, row in sample_ticks.iterrows():
        date_str = str(row['date'])
        time_str = str(row['time'])
        datetime_combinations.append(f"{date_str}_{time_str}")
    
    snapshot_config = SnapshotConfig.specified_times(datetime_combinations, levels=10)
    
    # 创建系统
    system = AdvancedMatchingSystem(config)
    system.snapshot_generator = SnapshotGenerator(snapshot_config)
    
    # 处理数据
    data_files = {
        'tick_file': tick_file,
        'trade_file': None,
        'order_file': order_file
    }
    
    result = system.process_security('161116', 'szl2', data_files)
    
    print(f"\n=== 处理结果 ===")
    print(f"订单处理: {result['orders_processed']}")
    print(f"成交生成: {result['trades_generated']}")
    print(f"快照生成: {result['snapshots_generated']}")
    print(f"处理时间: {result['processing_time']:.2f}秒")
    
    # 检查生成的快照文件
    snapshot_file = f"output/snapshots_{config.security_id}_{config.market}.csv"
    if os.path.exists(snapshot_file):
        snapshot_df = pd.read_csv(snapshot_file)
        print(f"快照文件: {len(snapshot_df)} 条记录")
        
        # 检查前几个快照的时间和数据
        print(f"\n前10个快照:")
        for i, row in snapshot_df.head(10).iterrows():
            print(f"  {i+1}: {row['date']}_{row['time']} - bid_qty={row['total_bid_qty']}, offer_qty={row['total_offer_qty']}")
        
        # 检查是否有非零的订单簿数据
        non_zero_snapshots = snapshot_df[
            (snapshot_df['total_bid_qty'] > 0) | 
            (snapshot_df['total_offer_qty'] > 0)
        ]
        print(f"\n有数据的快照: {len(non_zero_snapshots)} / {len(snapshot_df)} ({len(non_zero_snapshots)/len(snapshot_df)*100:.1f}%)")
        
        if len(non_zero_snapshots) > 0:
            print(f"前5个有数据的快照:")
            for i, row in non_zero_snapshots.head(5).iterrows():
                print(f"  {row['date']}_{row['time']}: bid_qty={row['total_bid_qty']}, offer_qty={row['total_offer_qty']}")
    
    # 检查成交文件
    trade_file = f"output/trades_{config.security_id}_{config.market}.csv"
    if os.path.exists(trade_file):
        trade_df = pd.read_csv(trade_file)
        print(f"\n成交文件: {len(trade_df)} 条记录")
        
        if len(trade_df) > 0:
            print(f"前5笔成交:")
            for i, row in trade_df.head(5).iterrows():
                print(f"  {i+1}: {row['date']}_{row['time']} - {row['price']}@{row['volume']}")
    
    return result

if __name__ == "__main__":
    from snapshot_generator import SnapshotGenerator
    test_fixed_matching()
