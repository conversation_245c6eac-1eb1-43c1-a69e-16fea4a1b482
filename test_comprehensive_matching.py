#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合测试脚本 - 测试上交所和深交所数据处理
"""

import pandas as pd
import numpy as np
from advanced_matching_system import AdvancedMatchingSystem, ProcessingConfig
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_shanghai_exchange():
    """测试上交所数据处理"""
    print("\n=== 测试上交所数据处理 ===")
    
    # 创建配置
    config = ProcessingConfig.create_event_driven_config('output')
    config.progress_report_interval = 5000  # 减少进度报告频率
    
    # 创建系统
    system = AdvancedMatchingSystem(config)
    
    # 上交所数据配置
    sh_config = {
        'security_id': '518880',
        'market': 'shl2',
        'data_files': {
            'tick': 'data/hq-shl2-518880-1-20250807165431662.csv',
            'trade': 'data/hq-shl2-518880-3-20250807134544227.csv',
            'order': 'data/hq-shl2-518880-4-20250807133643043.csv'
        }
    }
    
    start_time = time.time()
    
    try:
        # 处理上交所数据
        result = system.process_security(
            security_id=sh_config['security_id'],
            market=sh_config['market'],
            data_files=sh_config['data_files']
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n上交所处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  处理时间: {processing_time:.2f}秒")
        
        if 'processing_stats' in result:
            stats = result['processing_stats']
            print(f"  订单处理: {stats.get('orders_processed', 0)}")
            print(f"  成交生成: {stats.get('trades_generated', 0)}")
            print(f"  快照生成: {stats.get('snapshots_generated', 0)}")
        
        if 'validation_results' in result and result['validation_results']:
            validation = result['validation_results'].get('tick_validation')
            if validation:
                print(f"  验证准确率: {validation.overall_accuracy:.2%}")
                print(f"  时间匹配: {validation.time_matches}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"上交所处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_shenzhen_exchange():
    """测试深交所数据处理"""
    print("\n=== 测试深交所数据处理 ===")
    
    # 创建配置
    config = ProcessingConfig.create_event_driven_config('output')
    config.progress_report_interval = 5000
    
    # 创建系统
    system = AdvancedMatchingSystem(config)
    
    # 深交所数据配置
    sz_config = {
        'security_id': '161116',
        'market': 'szl2',
        'data_files': {
            'tick': 'data/hq-szl2-161116-1-20250807165417054.csv',
            'trade': None,  # 深交所没有trade文件
            'order': 'data/hq-szl2-161116-4-20250807134642187.csv'
        }
    }
    
    start_time = time.time()
    
    try:
        # 处理深交所数据
        result = system.process_security(
            security_id=sz_config['security_id'],
            market=sz_config['market'],
            data_files=sz_config['data_files']
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"\n深交所处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  处理时间: {processing_time:.2f}秒")
        
        if 'processing_stats' in result:
            stats = result['processing_stats']
            print(f"  订单处理: {stats.get('orders_processed', 0)}")
            print(f"  成交生成: {stats.get('trades_generated', 0)}")
            print(f"  快照生成: {stats.get('snapshots_generated', 0)}")
        
        if 'validation_results' in result and result['validation_results']:
            validation = result['validation_results'].get('tick_validation')
            if validation:
                print(f"  验证准确率: {validation.overall_accuracy:.2%}")
                print(f"  时间匹配: {validation.time_matches}")
                
                # 显示最佳匹配字段
                if hasattr(validation, 'field_matches'):
                    best_fields = []
                    for field, stats in validation.field_matches.items():
                        if stats.get('match_rate', 0) > 0:
                            best_fields.append((field, stats['match_rate']))
                    
                    if best_fields:
                        best_fields.sort(key=lambda x: x[1], reverse=True)
                        print(f"  最佳匹配字段:")
                        for field, rate in best_fields[:3]:
                            print(f"    {field}: {rate:.2%}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"深交所处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_validation_results():
    """分析验证结果"""
    print("\n=== 分析验证结果 ===")
    
    try:
        # 读取生成的快照数据
        sz_snapshots = pd.read_csv('output/snapshots_161116_szl2.csv')
        sz_official = pd.read_csv('data/hq-szl2-161116-1-20250807165417054.csv')
        
        print(f"深交所生成快照: {len(sz_snapshots)} 条")
        print(f"深交所官方tick: {len(sz_official)} 条")
        
        # 分析时间匹配情况
        generated_times = set(str(t) for t in sz_snapshots['time'])
        official_times = set(str(t) for t in sz_official['time'])
        
        matches = generated_times.intersection(official_times)
        print(f"时间戳匹配: {len(matches)} 条")
        
        if matches:
            print("匹配时间戳样本:")
            for i, match in enumerate(sorted(matches)[:5]):
                print(f"  {match}")
        
        # 分析字段差异
        if matches:
            sample_time = list(matches)[0]
            
            # 获取对应的数据行
            gen_row = sz_snapshots[sz_snapshots['time'] == int(sample_time)].iloc[0]
            off_row = sz_official[sz_official['time'] == int(sample_time)].iloc[0]
            
            print(f"\n样本时间戳 {sample_time} 的字段对比:")
            
            # 比较关键字段
            key_fields = ['num_trades', 'total_volume_trade', 'total_value_trade']
            for field in key_fields:
                gen_val = gen_row.get(field, 'N/A')
                off_val = off_row.get(field, 'N/A')
                print(f"  {field}: 生成={gen_val}, 官方={off_val}")
        
    except Exception as e:
        print(f"分析失败: {e}")


def main():
    """主函数"""
    print("=== 综合测试脚本 ===")
    
    # 测试深交所（较快）
    sz_success = test_shenzhen_exchange()
    
    # 分析验证结果
    if sz_success:
        analyze_validation_results()
    
    # 测试上交所（较慢，可选）
    print(f"\n是否测试上交所数据？(上交所数据量大，需要较长时间)")
    test_sh = input("输入 'y' 继续测试上交所，其他键跳过: ").lower() == 'y'
    
    if test_sh:
        sh_success = test_shanghai_exchange()
    else:
        sh_success = None
        print("跳过上交所测试")
    
    # 总结
    print(f"\n=== 测试总结 ===")
    print(f"深交所测试: {'✅ 成功' if sz_success else '❌ 失败'}")
    if sh_success is not None:
        print(f"上交所测试: {'✅ 成功' if sh_success else '❌ 失败'}")
    else:
        print(f"上交所测试: ⏭️ 跳过")


if __name__ == "__main__":
    main()
