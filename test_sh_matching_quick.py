#!/usr/bin/env python3
"""快速测试上交所数据处理"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_matching_system import AdvancedMatchingSystem, ProcessingConfig
from snapshot_generator import SnapshotConfig
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_sh_quick():
    """快速测试上交所数据处理"""
    
    print("=== 快速测试上交所518880数据处理 ===")
    
    # 配置
    config = ProcessingConfig()
    config.security_id = '518880'
    config.market = 'shl2'
    config.progress_report_interval = 5000
    
    # 数据文件
    tick_file = 'data/hq-shl2-518880-1-20250807165431662.csv'
    trade_file = 'data/hq-shl2-518880-3-20250807134544227.csv'
    order_file = 'data/hq-shl2-518880-4-20250807133643043.csv'
    
    # 创建快照配置（只取前1000个时间点进行快速测试）
    import pandas as pd
    official_tick_df = pd.read_csv(tick_file)
    
    # 只取前1000个时间点
    sample_ticks = official_tick_df.head(1000)
    datetime_combinations = []
    for _, row in sample_ticks.iterrows():
        date_str = str(row['date'])
        time_str = str(row['time'])
        datetime_combinations.append(f"{date_str}_{time_str}")
    
    snapshot_config = SnapshotConfig.specified_times(datetime_combinations, levels=10)
    
    # 创建系统
    system = AdvancedMatchingSystem(config)

    # 处理数据
    data_files = {
        'tick_file': tick_file,
        'trade_file': trade_file,
        'order_file': order_file
    }
    result = system.process_security('518880', 'shl2', data_files)
    
    print(f"\n=== 处理结果 ===")
    print(f"订单处理: {result['orders_processed']}")
    print(f"成交生成: {result['trades_generated']}")
    print(f"快照生成: {result['snapshots_generated']}")
    print(f"处理时间: {result['processing_time']:.2f}秒")
    
    # 检查生成的快照文件
    snapshot_file = f"output/snapshots_{config.security_id}_{config.market}.csv"
    if os.path.exists(snapshot_file):
        snapshot_df = pd.read_csv(snapshot_file)
        print(f"快照文件: {len(snapshot_df)} 条记录")
        
        # 检查前几个快照的时间
        print(f"\n前10个快照时间:")
        for i, row in snapshot_df.head(10).iterrows():
            print(f"  {i+1}: {row['date']}_{row['time']}")
        
        # 检查是否有集合竞价时间段的快照
        auction_snapshots = snapshot_df[
            (snapshot_df['time'] >= 91500000) & 
            (snapshot_df['time'] <= 92500000)
        ]
        print(f"\n集合竞价时间段快照: {len(auction_snapshots)} 条")
        
        if len(auction_snapshots) > 0:
            print("前5个集合竞价快照:")
            for i, row in auction_snapshots.head(5).iterrows():
                print(f"  {row['date']}_{row['time']}: bid_qty={row['total_bid_qty']}, offer_qty={row['total_offer_qty']}")
    
    return result

if __name__ == "__main__":
    test_sh_quick()
