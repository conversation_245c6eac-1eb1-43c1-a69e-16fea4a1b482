#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证对比模块
实现生成数据与官方数据的详细对比验证
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
import json
from datetime import datetime
from snapshot_generator import TickSnapshot

logger = logging.getLogger(__name__)


class ValidationResult:
    """验证结果数据结构"""
    
    def __init__(self):
        self.total_generated = 0
        self.total_official = 0
        self.time_matches = 0
        self.field_matches = {}
        self.overall_accuracy = 0.0
        self.detailed_differences = []
        self.summary = {}
        
    def to_dict(self) -> Dict:
        return {
            'total_generated': self.total_generated,
            'total_official': self.total_official,
            'time_matches': self.time_matches,
            'field_matches': self.field_matches,
            'overall_accuracy': self.overall_accuracy,
            'detailed_differences': self.detailed_differences,
            'summary': self.summary
        }


class FieldValidator:
    """字段验证器"""
    
    @staticmethod
    def validate_numeric_field(official_val, generated_val, tolerance: float = 0.01) -> Tu<PERSON>[bool, Dict]:
        """验证数值字段"""
        # 处理NaN和None值
        if pd.isna(official_val) or official_val is None:
            official_val = 0
        if pd.isna(generated_val) or generated_val is None:
            generated_val = 0
        
        try:
            official_float = float(official_val)
            generated_float = float(generated_val)
            
            diff = abs(official_float - generated_float)
            is_match = diff <= tolerance
            
            return is_match, {
                'official': official_float,
                'generated': generated_float,
                'difference': diff,
                'tolerance': tolerance,
                'is_match': is_match
            }
        except (ValueError, TypeError) as e:
            return False, {
                'error': f"数值转换失败: {e}",
                'official': official_val,
                'generated': generated_val
            }
    
    @staticmethod
    def validate_array_field(official_val, generated_val, tolerance: float = 0.01) -> Tuple[bool, Dict]:
        """验证数组字段（如价格、数量数组）"""
        try:
            # 解析官方数据中的数组格式
            if isinstance(official_val, str):
                # 处理类似 "[74460 0]" 的格式
                official_array = eval(official_val.replace(' ', ','))
            else:
                official_array = official_val
            
            if isinstance(generated_val, (list, tuple)):
                generated_array = list(generated_val)
            else:
                generated_array = generated_val
            
            # 确保长度一致
            if len(official_array) != len(generated_array):
                return False, {
                    'error': '数组长度不匹配',
                    'official_length': len(official_array),
                    'generated_length': len(generated_array)
                }
            
            # 逐个比较元素
            matches = []
            for i, (off_val, gen_val) in enumerate(zip(official_array, generated_array)):
                is_match, _ = FieldValidator.validate_numeric_field(off_val, gen_val, tolerance)
                matches.append(is_match)
            
            overall_match = all(matches)
            
            return overall_match, {
                'official': official_array,
                'generated': generated_array,
                'element_matches': matches,
                'overall_match': overall_match
            }
            
        except Exception as e:
            return False, {
                'error': f"数组验证失败: {e}",
                'official': official_val,
                'generated': generated_val
            }


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.field_validator = FieldValidator()
        
    def validate_tick_data(self, generated_snapshots: List[TickSnapshot], 
                          official_tick_file: str) -> ValidationResult:
        """验证tick数据"""
        result = ValidationResult()
        
        try:
            # 读取官方数据
            official_df = pd.read_csv(official_tick_file)
            result.total_official = len(official_df)
            result.total_generated = len(generated_snapshots)
            
            # 创建生成数据的时间索引
            generated_by_time = {snapshot.timestamp: snapshot for snapshot in generated_snapshots}
            
            # 定义要验证的字段
            validation_fields = {
                'num_trades': {'type': 'numeric', 'tolerance': 0.01},
                'total_volume_trade': {'type': 'numeric', 'tolerance': 0.01},
                'total_value_trade': {'type': 'numeric', 'tolerance': 0.01},
                'total_bid_qty': {'type': 'numeric', 'tolerance': 0.01},
                'total_offer_qty': {'type': 'numeric', 'tolerance': 0.01},
                'weighted_avg_bid_price': {'type': 'numeric', 'tolerance': 0.01},
                'weighted_avg_offer_price': {'type': 'numeric', 'tolerance': 0.01},
                'bid_prices': {'type': 'array', 'tolerance': 0.01},
                'bid_volumes': {'type': 'array', 'tolerance': 0.01},
                'offer_prices': {'type': 'array', 'tolerance': 0.01},
                'offer_volumes': {'type': 'array', 'tolerance': 0.01}
            }
            
            # 初始化字段匹配统计
            for field in validation_fields:
                result.field_matches[field] = {
                    'matches': 0,
                    'total': 0,
                    'match_rate': 0.0
                }
            
            # 逐行验证
            for idx, official_row in official_df.iterrows():
                official_time = str(official_row['time'])
                
                if official_time in generated_by_time:
                    result.time_matches += 1
                    generated_snapshot = generated_by_time[official_time]
                    
                    # 验证各个字段
                    row_differences = {}
                    
                    for field, config in validation_fields.items():
                        official_val = official_row.get(field)
                        
                        # 获取生成数据中的对应值
                        if hasattr(generated_snapshot, field):
                            generated_val = getattr(generated_snapshot, field)
                        else:
                            generated_val = None
                        
                        # 根据字段类型进行验证
                        if config['type'] == 'numeric':
                            is_match, details = self.field_validator.validate_numeric_field(
                                official_val, generated_val, config['tolerance']
                            )
                        elif config['type'] == 'array':
                            is_match, details = self.field_validator.validate_array_field(
                                official_val, generated_val, config['tolerance']
                            )
                        else:
                            is_match, details = False, {'error': f'未知字段类型: {config["type"]}'}
                        
                        # 更新统计
                        result.field_matches[field]['total'] += 1
                        if is_match:
                            result.field_matches[field]['matches'] += 1
                        else:
                            row_differences[field] = details
                    
                    # 记录差异（只保留前10个样本）
                    if row_differences and len(result.detailed_differences) < 10:
                        result.detailed_differences.append({
                            'timestamp': official_time,
                            'differences': row_differences
                        })
            
            # 计算匹配率
            total_field_matches = 0
            total_field_comparisons = 0
            
            for field, stats in result.field_matches.items():
                if stats['total'] > 0:
                    stats['match_rate'] = stats['matches'] / stats['total']
                    total_field_matches += stats['matches']
                    total_field_comparisons += stats['total']
            
            # 计算总体准确率
            if total_field_comparisons > 0:
                result.overall_accuracy = total_field_matches / total_field_comparisons
            
            # 生成摘要
            result.summary = {
                'time_match_rate': result.time_matches / result.total_official if result.total_official > 0 else 0,
                'overall_accuracy': result.overall_accuracy,
                'best_matching_fields': [],
                'worst_matching_fields': [],
                'total_differences': len(result.detailed_differences)
            }
            
            # 找出匹配最好和最差的字段
            field_rates = [(field, stats['match_rate']) for field, stats in result.field_matches.items()]
            field_rates.sort(key=lambda x: x[1], reverse=True)
            
            result.summary['best_matching_fields'] = field_rates[:3]
            result.summary['worst_matching_fields'] = field_rates[-3:]
            
            logger.info(f"验证完成: 总体准确率 {result.overall_accuracy:.2%}")
            
        except Exception as e:
            logger.error(f"验证过程出错: {e}")
            result.summary['error'] = str(e)
        
        return result
    
    def validate_trade_data(self, generated_trades: List[Dict], official_trade_file: str) -> ValidationResult:
        """验证trade数据"""
        result = ValidationResult()
        
        try:
            # 读取官方trade数据
            official_df = pd.read_csv(official_trade_file)
            result.total_official = len(official_df)
            result.total_generated = len(generated_trades)
            
            # 创建生成数据的索引
            generated_by_index = {trade.get('timestamp', i): trade for i, trade in enumerate(generated_trades)}
            
            # 验证字段
            validation_fields = {
                'trade_price': {'type': 'numeric', 'tolerance': 0.01},
                'trade_volume': {'type': 'numeric', 'tolerance': 0.01}
            }
            
            # 初始化统计
            for field in validation_fields:
                result.field_matches[field] = {
                    'matches': 0,
                    'total': 0,
                    'match_rate': 0.0
                }
            
            # 逐行验证（简化版本）
            for idx, official_row in official_df.iterrows():
                biz_index = official_row.get('biz_index')
                
                if biz_index in generated_by_index:
                    result.time_matches += 1
                    generated_trade = generated_by_index[biz_index]
                    
                    # 验证价格和数量
                    for field, config in validation_fields.items():
                        official_val = official_row.get(field)
                        generated_val = generated_trade.get(field.replace('trade_', ''))
                        
                        is_match, _ = self.field_validator.validate_numeric_field(
                            official_val, generated_val, config['tolerance']
                        )
                        
                        result.field_matches[field]['total'] += 1
                        if is_match:
                            result.field_matches[field]['matches'] += 1
            
            # 计算匹配率
            for field, stats in result.field_matches.items():
                if stats['total'] > 0:
                    stats['match_rate'] = stats['matches'] / stats['total']
            
            logger.info(f"Trade验证完成: 时间匹配 {result.time_matches}/{result.total_official}")
            
        except Exception as e:
            logger.error(f"Trade验证出错: {e}")
            result.summary['error'] = str(e)
        
        return result


class ValidationReporter:
    """验证报告生成器"""
    
    @staticmethod
    def generate_report(validation_results: Dict[str, ValidationResult], output_file: str = None) -> Dict:
        """生成验证报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'validation_results': {},
            'overall_summary': {}
        }
        
        # 汇总各个验证结果
        total_accuracy = 0
        valid_results = 0
        
        for name, result in validation_results.items():
            report['validation_results'][name] = result.to_dict()
            
            if result.overall_accuracy > 0:
                total_accuracy += result.overall_accuracy
                valid_results += 1
        
        # 计算总体摘要
        if valid_results > 0:
            report['overall_summary'] = {
                'average_accuracy': total_accuracy / valid_results,
                'total_validations': len(validation_results),
                'successful_validations': valid_results,
                'failed_validations': len(validation_results) - valid_results
            }
        
        # 保存报告
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"验证报告已保存到: {output_file}")
        
        return report
    
    @staticmethod
    def print_summary(validation_result: ValidationResult):
        """打印验证摘要"""
        print(f"\n=== 验证结果摘要 ===")
        print(f"生成数据: {validation_result.total_generated} 条")
        print(f"官方数据: {validation_result.total_official} 条")
        print(f"时间匹配: {validation_result.time_matches} 条")
        print(f"总体准确率: {validation_result.overall_accuracy:.2%}")
        
        if validation_result.field_matches:
            print(f"\n字段匹配率:")
            for field, stats in validation_result.field_matches.items():
                print(f"  {field}: {stats['match_rate']:.2%} ({stats['matches']}/{stats['total']})")
        
        if validation_result.summary.get('best_matching_fields'):
            print(f"\n最佳匹配字段:")
            for field, rate in validation_result.summary['best_matching_fields']:
                print(f"  {field}: {rate:.2%}")
        
        if validation_result.summary.get('worst_matching_fields'):
            print(f"\n待改进字段:")
            for field, rate in validation_result.summary['worst_matching_fields']:
                print(f"  {field}: {rate:.2%}")
        
        if validation_result.detailed_differences:
            print(f"\n样本差异 (前3个):")
            for i, diff in enumerate(validation_result.detailed_differences[:3]):
                print(f"  时间戳 {diff['timestamp']}: {len(diff['differences'])} 个字段有差异")
