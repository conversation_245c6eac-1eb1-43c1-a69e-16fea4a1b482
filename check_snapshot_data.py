#!/usr/bin/env python3
"""检查快照数据质量"""

import pandas as pd

def check_snapshot_data():
    """检查生成的快照数据"""
    
    # 读取快照数据
    snapshot_file = 'output/snapshots_161116_szl2.csv'
    df = pd.read_csv(snapshot_file)
    
    print(f"=== 快照数据质量检查 ===")
    print(f"总快照数: {len(df)}")
    
    # 检查有数据的快照
    non_zero_bid = df[df['total_bid_qty'] > 0]
    non_zero_offer = df[df['total_offer_qty'] > 0]
    non_zero_trades = df[df['num_trades'] > 0]
    
    print(f"有买单数据的快照: {len(non_zero_bid)}")
    print(f"有卖单数据的快照: {len(non_zero_offer)}")
    print(f"有成交数据的快照: {len(non_zero_trades)}")
    
    if len(non_zero_bid) > 0:
        print(f"\n前5个有买单数据的快照:")
        for i, row in non_zero_bid.head(5).iterrows():
            print(f"  {row['date']}_{row['time']}: 买单={row['total_bid_qty']}, 卖单={row['total_offer_qty']}, 成交={row['num_trades']}")
    
    if len(non_zero_trades) > 0:
        print(f"\n前5个有成交数据的快照:")
        for i, row in non_zero_trades.head(5).iterrows():
            print(f"  {row['date']}_{row['time']}: 成交笔数={row['num_trades']}, 成交量={row['total_volume_trade']}, 成交额={row['total_value_trade']}")
    
    # 检查交易阶段分布
    phase_counts = df['trading_phase_code'].value_counts()
    print(f"\n交易阶段分布:")
    for phase, count in phase_counts.items():
        print(f"  {phase}: {count} 个快照")
    
    # 检查集合竞价阶段的数据
    auction_phase = df[df['trading_phase_code'] == 'C']
    print(f"\n集合竞价阶段快照: {len(auction_phase)}")
    auction_with_data = auction_phase[(auction_phase['total_bid_qty'] > 0) | (auction_phase['total_offer_qty'] > 0)]
    print(f"集合竞价阶段有订单簿数据的快照: {len(auction_with_data)}")
    
    # 检查连续竞价阶段的数据
    continuous_phase = df[df['trading_phase_code'] == 'T']
    print(f"\n连续竞价阶段快照: {len(continuous_phase)}")
    continuous_with_data = continuous_phase[(continuous_phase['total_bid_qty'] > 0) | (continuous_phase['total_offer_qty'] > 0)]
    print(f"连续竞价阶段有订单簿数据的快照: {len(continuous_with_data)}")
    
    if len(continuous_with_data) > 0:
        print(f"\n连续竞价阶段有数据的快照样本:")
        for i, row in continuous_with_data.head(3).iterrows():
            print(f"  {row['date']}_{row['time']}: 买单={row['total_bid_qty']}, 卖单={row['total_offer_qty']}")

if __name__ == "__main__":
    check_snapshot_data()
