#!/usr/bin/env python3
"""调试订单解析的脚本"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_parser import SHExchangeParser
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_order_parsing():
    """调试上交所订单解析"""

    # 创建解析器
    parser = SHExchangeParser('518880')

    # 解析数据
    tick_file = 'data/hq-shl2-518880-1-20250807165431662.csv'
    trade_file = 'data/hq-shl2-518880-3-20250807134544227.csv'
    order_file = 'data/hq-shl2-518880-4-20250807133643043.csv'

    # 分别解析两个数据源
    import pandas as pd
    trade_df = pd.read_csv(trade_file) if trade_file else pd.DataFrame()
    order_df = pd.read_csv(order_file) if order_file else pd.DataFrame()

    reconstructed_orders = parser._reconstruct_orders_from_trades(trade_df)
    pending_orders = parser._parse_pending_orders(order_df)

    print(f"\n=== 数据源分析 ===")
    print(f"从成交数据重构的订单数: {len(reconstructed_orders)}")
    print(f"从订单数据解析的订单数: {len(pending_orders)}")

    if reconstructed_orders:
        reconstructed_ids = [int(float(o.order_id)) for o in reconstructed_orders if o.order_id.replace('.', '').replace(' ', '').isdigit()]
        print(f"重构订单ID范围: {min(reconstructed_ids)} - {max(reconstructed_ids)}")

    if pending_orders:
        pending_ids = [int(float(o.order_id)) for o in pending_orders if o.order_id.replace('.', '').replace(' ', '').isdigit()]
        print(f"待处理订单ID范围: {min(pending_ids)} - {max(pending_ids)}")

    orders, trades = parser.parse_data_files(tick_file, trade_file, order_file)

    print(f"\n=== 订单解析结果 ===")
    print(f"总订单数: {len(orders)}")
    print(f"总成交数: {len(trades)}")

    # 检查前20个订单
    print(f"\n=== 前20个订单 ===")
    for i, order in enumerate(orders[:20]):
        print(f"{i+1:3d}: order_id={order.order_id:>8s}, time={order.time}, date={order.date}, price={order.price}, volume={order.volume}, side={order.side}")

    # 检查9:15-9:25时间段的订单
    print(f"\n=== 9:15-9:25时间段的订单 ===")
    auction_orders = [o for o in orders if 91500000 <= o.time <= 92500000]
    print(f"集合竞价订单数: {len(auction_orders)}")

    for i, order in enumerate(auction_orders[:10]):
        print(f"{i+1:3d}: order_id={order.order_id:>8s}, time={order.time}, price={order.price}, volume={order.volume}, side={order.side}")

    # 检查第一笔成交
    if trades:
        print(f"\n=== 前10笔成交 ===")
        for i, trade in enumerate(trades[:10]):
            print(f"{i+1:3d}: trade_id={trade.trade_id}, time={trade.time}, price={trade.price}, volume={trade.volume}")

    # 检查订单ID排序问题
    print(f"\n=== 订单ID排序问题分析 ===")
    # 检查原始order_id的数值范围
    order_ids = [int(float(o.order_id)) for o in orders if o.order_id.replace('.', '').replace(' ', '').isdigit()]
    if order_ids:
        print(f"order_id数值范围: {min(order_ids)} - {max(order_ids)}")
        print(f"数值型order_id数量: {len(order_ids)}")

    # 检查字符串排序vs数值排序的差异
    sample_order_ids = [o.order_id for o in orders[:50]]
    print(f"\n当前排序前10个: {sample_order_ids[:10]}")

    # 按数值排序
    numeric_sorted = sorted([o for o in orders if o.order_id.replace('.', '').replace(' ', '').isdigit()],
                           key=lambda x: int(float(x.order_id)))
    print(f"理论数值排序前10个: {[o.order_id for o in numeric_sorted[:10]]}")

    # 检查最小order_id的订单详情
    min_order = numeric_sorted[0]
    print(f"\n最小order_id订单详情: id={min_order.order_id}, time={min_order.time}, price={min_order.price}, side={min_order.side}")

    # 检查为什么最小的order_id没有出现在前面
    print(f"\n=== 检查最小order_id为什么没在前面 ===")
    min_order_id = min(order_ids)
    min_orders = [o for o in orders if int(float(o.order_id)) == min_order_id]
    if min_orders:
        print(f"最小order_id {min_order_id} 的订单数量: {len(min_orders)}")
        for i, order in enumerate(min_orders[:3]):
            print(f"  {i+1}: time={order.time}, price={order.price}, side={order.side}")

    # 查找这个订单在整个列表中的位置
    for i, order in enumerate(orders):
        if int(float(order.order_id)) == min_order_id:
            print(f"最小order_id在列表中的位置: {i+1}")
            break

    # 检查集合竞价时间段的订单是否按时间正确排序
    print(f"\n=== 集合竞价时间段订单时间分析 ===")
    auction_times = [o.time for o in auction_orders[:20]]
    print(f"前20个集合竞价订单时间: {auction_times}")
    print(f"时间是否递增: {auction_times == sorted(auction_times)}")

if __name__ == "__main__":
    debug_order_parsing()
