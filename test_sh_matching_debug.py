#!/usr/bin/env python3
"""深入调试上交所数据处理和撮合机制"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_parser import SHExchangeParser
from matching_engine import MatchingEngine
from snapshot_generator import SnapshotGenerator, SnapshotConfig
import pandas as pd
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_sh_matching():
    """深入调试上交所撮合机制"""
    
    print("=== 深入调试上交所518880撮合机制 ===")
    
    # 数据文件
    tick_file = 'data/hq-shl2-518880-1-20250807165431662.csv'
    trade_file = 'data/hq-shl2-518880-3-20250807134544227.csv'
    order_file = 'data/hq-shl2-518880-4-20250807133643043.csv'
    
    # 1. 解析数据
    print("\n=== 步骤1: 解析数据 ===")
    parser = SHExchangeParser('518880')
    orders, trades = parser.parse_data_files(tick_file, trade_file, order_file)
    
    print(f"解析结果:")
    print(f"  订单总数: {len(orders)}")
    print(f"  成交总数: {len(trades)}")
    
    # 检查前20个订单
    print(f"\n前20个订单:")
    for i, order in enumerate(orders[:20]):
        print(f"  {i+1:2d}: id={order.order_id:>8s}, time={order.time}, price={order.price:>8.1f}, vol={order.volume:>6d}, side={order.side}, type={order.order_type}")
    
    # 2. 分析集合竞价订单
    print(f"\n=== 步骤2: 分析集合竞价订单 ===")
    auction_orders = [o for o in orders if 91500000 <= o.time <= 92500000]
    print(f"集合竞价订单数: {len(auction_orders)}")
    
    if auction_orders:
        print(f"集合竞价订单样本:")
        for i, order in enumerate(auction_orders[:10]):
            print(f"  {i+1:2d}: id={order.order_id:>8s}, time={order.time}, price={order.price:>8.1f}, vol={order.volume:>6d}, side={order.side}")
    
    # 3. 手动测试撮合引擎
    print(f"\n=== 步骤3: 手动测试撮合引擎 ===")
    engine = MatchingEngine()
    
    # 处理前100个订单，观察订单簿变化
    test_orders = orders[:100]
    print(f"测试前100个订单的处理:")
    
    for i, order in enumerate(test_orders):
        # 确定交易阶段
        from data_parser import TimeParser
        trading_phase = TimeParser.get_trading_phase(str(order.time), 'shl2')
        
        # 处理订单前的订单簿状态
        before_buy_levels = len(engine.order_book.buy_orders)
        before_sell_levels = len(engine.order_book.sell_orders)
        
        # 处理订单
        generated_trades = engine.process_order(order, trading_phase)
        
        # 处理订单后的订单簿状态
        after_buy_levels = len(engine.order_book.buy_orders)
        after_sell_levels = len(engine.order_book.sell_orders)
        
        # 如果订单簿有变化或产生成交，打印详细信息
        if (before_buy_levels != after_buy_levels or 
            before_sell_levels != after_sell_levels or 
            len(generated_trades) > 0):
            
            print(f"\n  订单 {i+1}: id={order.order_id}, time={order.time}, phase={trading_phase}")
            print(f"    订单: price={order.price}, vol={order.volume}, side={order.side}, type={order.order_type}")
            print(f"    订单簿变化: 买单层数 {before_buy_levels}->{after_buy_levels}, 卖单层数 {before_sell_levels}->{after_sell_levels}")
            print(f"    产生成交: {len(generated_trades)} 笔")
            
            if len(generated_trades) > 0:
                for j, trade in enumerate(generated_trades):
                    print(f"      成交{j+1}: price={trade.price}, vol={trade.volume}")
        
        # 每10个订单报告一次订单簿状态
        if (i + 1) % 10 == 0:
            total_bid_qty = sum(
                sum(order['volume'] for order in orders_list)
                for orders_list in engine.order_book.buy_orders.values()
            )
            total_offer_qty = sum(
                sum(order['volume'] for order in orders_list)
                for orders_list in engine.order_book.sell_orders.values()
            )
            print(f"  处理{i+1}个订单后: 买单总量={total_bid_qty}, 卖单总量={total_offer_qty}")
    
    # 4. 检查最终订单簿状态
    print(f"\n=== 步骤4: 检查最终订单簿状态 ===")
    print(f"买单价格层数: {len(engine.order_book.buy_orders)}")
    print(f"卖单价格层数: {len(engine.order_book.sell_orders)}")
    
    if engine.order_book.buy_orders:
        print(f"买单前5个价格层:")
        sorted_buy_prices = sorted(engine.order_book.buy_orders.keys(), reverse=True)
        for i, price in enumerate(sorted_buy_prices[:5]):
            orders_at_price = engine.order_book.buy_orders[price]
            total_vol = sum(order['volume'] for order in orders_at_price)
            print(f"  价格 {price}: {len(orders_at_price)} 个订单, 总量 {total_vol}")
    
    if engine.order_book.sell_orders:
        print(f"卖单前5个价格层:")
        sorted_sell_prices = sorted(engine.order_book.sell_orders.keys())
        for i, price in enumerate(sorted_sell_prices[:5]):
            orders_at_price = engine.order_book.sell_orders[price]
            total_vol = sum(order['volume'] for order in orders_at_price)
            print(f"  价格 {price}: {len(orders_at_price)} 个订单, 总量 {total_vol}")
    
    # 5. 测试快照生成
    print(f"\n=== 步骤5: 测试快照生成 ===")
    snapshot_config = SnapshotConfig()
    snapshot_config.levels = 10
    snapshot_generator = SnapshotGenerator(snapshot_config)
    
    # 生成一个快照
    snapshot = snapshot_generator.generate_snapshot("91500000", "C", engine)
    print(f"生成的快照:")
    print(f"  时间戳: {snapshot.timestamp}")
    print(f"  交易阶段: {snapshot.trading_phase}")
    print(f"  买单总量: {snapshot.total_bid_qty}")
    print(f"  卖单总量: {snapshot.total_offer_qty}")
    print(f"  买单价格: {snapshot.bid_prices[:5]}")
    print(f"  买单数量: {snapshot.bid_volumes[:5]}")
    print(f"  卖单价格: {snapshot.offer_prices[:5]}")
    print(f"  卖单数量: {snapshot.offer_volumes[:5]}")

    # 6. 深入分析问题
    print(f"\n=== 步骤6: 深入分析问题 ===")
    print(f"关键发现:")
    print(f"1. 集合竞价订单正确处理: 前100个订单都是集合竞价阶段(C)")
    print(f"2. 订单簿正确构建: 买单{len(engine.order_book.buy_orders)}层, 卖单{len(engine.order_book.sell_orders)}层")
    print(f"3. 集合竞价期间无成交: 符合预期，集合竞价只接受订单不撮合")
    print(f"4. 订单簿有数据但快照显示为0: 这是关键问题!")

    # 检查订单簿和快照的差异
    print(f"\n订单簿实际状态:")
    total_bid_qty_actual = sum(
        sum(order['volume'] for order in orders_list)
        for orders_list in engine.order_book.buy_orders.values()
    )
    total_offer_qty_actual = sum(
        sum(order['volume'] for order in orders_list)
        for orders_list in engine.order_book.sell_orders.values()
    )
    print(f"  实际买单总量: {total_bid_qty_actual}")
    print(f"  实际卖单总量: {total_offer_qty_actual}")
    print(f"  快照买单总量: {snapshot.total_bid_qty}")
    print(f"  快照卖单总量: {snapshot.total_offer_qty}")

    if total_bid_qty_actual != snapshot.total_bid_qty or total_offer_qty_actual != snapshot.total_offer_qty:
        print(f"❌ 发现问题: 订单簿实际数据与快照数据不一致!")
        print(f"   这说明快照生成逻辑有问题")
    else:
        print(f"✅ 订单簿数据与快照数据一致")
    
    return {
        'orders_count': len(orders),
        'trades_count': len(trades),
        'auction_orders_count': len(auction_orders),
        'final_buy_levels': len(engine.order_book.buy_orders),
        'final_sell_levels': len(engine.order_book.sell_orders),
        'snapshot': snapshot
    }

if __name__ == "__main__":
    result = debug_sh_matching()
    print(f"\n=== 调试总结 ===")
    print(f"订单总数: {result['orders_count']}")
    print(f"成交总数: {result['trades_count']}")
    print(f"集合竞价订单数: {result['auction_orders_count']}")
    print(f"最终买单层数: {result['final_buy_levels']}")
    print(f"最终卖单层数: {result['final_sell_levels']}")
